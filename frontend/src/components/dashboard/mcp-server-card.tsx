'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Trash2, Plus, Check } from 'lucide-react';
import { ComposioApp } from '@/types/composio';
import { getComposioAppIcon } from '@/lib/icon-mapping';

interface MCPServerCardProps {
  app: ComposioApp;
  isConnected: boolean;
  isConnecting: boolean;
  onConnect: () => void;
  onDisconnect: () => void;
  onViewTools?: () => void;
  isLoadingTools?: boolean;
}

export function MCPServerCard({
  app,
  isConnected,
  isConnecting,
  onConnect,
  onDisconnect,
  onViewTools,
  isLoadingTools = false
}: MCPServerCardProps) {
  return (
    <motion.div
      whileHover={{
        transition: { duration: 0.2, ease: "easeOut" }
      }}
      className="flex-shrink-0"
    >
      <Card
        className="w-48 h-16 cursor-pointer transition-all duration-200 hover:shadow-lg bg-muted/50 dark:bg-muted/30 border border-border rounded-2xl overflow-hidden"
        onClick={
          isConnected && onViewTools
            ? onViewTools
            : (!isConnected && !isConnecting ? onConnect : undefined)
        }
      >
        <CardContent className="p-0 h-full flex items-center">
          {/* Left: Icon taking up the full left side */}
          <div className="w-16 h-full flex items-center justify-center flex-shrink-0">
            {(() => {
              const IconComponent = getComposioAppIcon(app);
              return <IconComponent className="w-12 h-12" />;
            })()}
          </div>

          {/* Right: Text and Actions */}
          <div className="flex-1 flex items-center justify-between px-4 py-2 h-full">
            {/* App Text */}
            <div className="min-w-0 flex-1">
              <div className="font-medium text-sm text-foreground truncate leading-tight">
                {app.name}
              </div>
            </div>

            {/* Status/Actions */}
            <div className="flex items-center flex-shrink-0 ml-2">
              {isConnected ? (
                /* Connected state: Green check that becomes trash on hover, or loading spinner */
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDisconnect();
                  }}
                  className="h-8 w-8 p-0 rounded-full text-green-500 hover:text-destructive hover:bg-destructive/10 group"
                  disabled={isLoadingTools}
                >
                  {isLoadingTools ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <>
                      <Check className="h-4 w-4 group-hover:hidden" />
                      <Trash2 className="h-4 w-4 hidden group-hover:block" />
                    </>
                  )}
                </Button>
              ) : (
                /* Connect button with plus icon */
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onConnect();
                  }}
                  disabled={isConnecting}
                  className="h-8 w-8 p-0 rounded-full hover:bg-primary/5"
                >
                  {isConnecting ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Plus className="h-4 w-4" />
                  )}
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
